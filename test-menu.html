<!DOCTYPE html>
<html lang="en">
<head>
    <title>Radial Menu Test - QB Framework</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.1/css/all.css" integrity="sha384-vp86vTRFVJgpjF9jiIGPEEqYqlDwgyBgEF109VFjmqGmIY/Y4HV4d3Gp2irVfcrp" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,500,500i,700,700i" rel="stylesheet">
    <link rel="stylesheet" href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="html/js/RadialMenu.js"></script>
    <link type="text/css" rel="stylesheet" href="html/css/RadialMenu.css">

    <style>
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: "Roboto", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .menuHolder {
            font-size: 10px;
        }

        #controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Roboto', sans-serif;
        }

        #controls h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        #controls p {
            margin: 5px 0;
            font-size: 14px;
        }

        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
            font-family: 'Roboto', sans-serif;
        }

        .btn:hover {
            background: #45a049;
        }

        .btn-danger {
            background: #f44336;
        }

        .btn-danger:hover {
            background: #da190b;
        }

        #console {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            height: 200px;
            background: rgba(0, 0, 0, 0.9);
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            border: 1px solid #333;
        }

        #console h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
            font-family: 'Roboto', sans-serif;
        }

        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }

        .log-click {
            color: #ffeb3b;
        }

        .log-action {
            color: #2196f3;
        }
    </style>
</head>
<body>


    <!-- Controls Panel -->
    <div id="controls">
        <h3>🎮 Radial Menu Test</h3>
        <p><strong>Controls:</strong></p>
        <p>• F1 - Toggle Menu</p>
        <p>• ESC - Close Menu</p>
        <p>• Arrow Keys - Navigate</p>
        <p>• Enter - Select</p>
        <p>• Mouse - Click to select</p>
        <br>
        <button class="btn" onclick="showMenu()">Show Menu</button>
        <button class="btn btn-danger" onclick="closeMenu()">Close Menu</button>
    </div>

    <!-- Console Log -->
    <div id="console">
        <h4>📋 Console Log</h4>
        <div id="console-content"></div>
    </div>

    <!-- Main Container -->
    <div id="container">
        <div id="navMenu"></div>
    </div>

    <script type="text/javascript">
        let menu = null;
        let consoleContent = document.getElementById('console-content');

        // Sample menu data with direct FontAwesome classes
        const sampleMenuData = [
            {
                id: 'citizen',
                title: 'Citizen',
                icon: 'fas fa-user-circle',
                functionName: 'citizen_action',
                functiontype: 'client',
                functionParameters: {},
                close: true,
                items: [
                    {
                        id: 'cuff',
                        title: 'Cuff Player',
                        icon: 'fas fa-handcuffs',
                        functionName: 'cuff_player',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    },
                    {
                        id: 'escort',
                        title: 'Escort Player',
                        icon: 'fas fa-people-arrows',
                        functionName: 'escort_player',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    }
                ]
            },
            {
                id: 'vehicle',
                title: 'Vehicle',
                icon: 'fas fa-car',
                functionName: 'vehicle_action',
                functiontype: 'client',
                functionParameters: {},
                close: true,
                items: [
                    {
                        id: 'engine',
                        title: 'Toggle Engine',
                        icon: 'fas fa-engine',
                        functionName: 'toggle_engine',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    },
                    {
                        id: 'doors',
                        title: 'Toggle Doors',
                        icon: 'fas fa-door-open',
                        functionName: 'toggle_doors',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    }
                ]
            },
            {
                id: 'animations',
                title: 'Animations',
                icon: 'fas fa-theater-masks',
                functionName: 'animations_menu',
                functiontype: 'client',
                functionParameters: {},
                close: false,
                items: [
                    {
                        id: 'dance',
                        title: 'Dance',
                        icon: 'fas fa-music',
                        functionName: 'play_dance',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    },
                    {
                        id: 'sit',
                        title: 'Sit Down',
                        icon: 'fas fa-chair',
                        functionName: 'play_sit',
                        functiontype: 'client',
                        functionParameters: {},
                        close: true
                    }
                ]
            },
            {
                id: 'job',
                title: 'Job Actions',
                icon: 'fas fa-briefcase',
                functionName: 'job_actions',
                functiontype: 'client',
                functionParameters: {},
                close: false
            },
            {
                id: 'inventory',
                title: 'Inventory',
                icon: 'fas fa-box-open',
                functionName: 'open_inventory',
                functiontype: 'client',
                functionParameters: {},
                close: true
            },
            {
                id: 'police',
                title: 'Police',
                icon: 'fas fa-shield-badge',
                functionName: 'police_menu',
                functiontype: 'client',
                functionParameters: {},
                close: false
            },
            {
                id: 'medical',
                title: 'Medical',
                icon: 'fas fa-kit-medical',
                functionName: 'medical_menu',
                functiontype: 'client',
                functionParameters: {},
                close: false
            }
        ];

        // Console logging function
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            consoleContent.appendChild(logEntry);
            consoleContent.scrollTop = consoleContent.scrollHeight;
        }

        // Function to show the menu
        function showMenu() {
            if (!menu) {
                $('#navMenu').empty();
                menu = new RadialMenu({
                    parent: document.getElementById('navMenu'),
                    size: 450,
                    closeOnClick: false,
                    menuItems: sampleMenuData,
                    onClick: function (item) {
                        logToConsole(`Clicked: ${item.title} (${item.id})`, 'click');
                        if(item.functionName !== 'undefined') {
                            logToConsole(`Action: ${item.functionName} | Type: ${item.functiontype}`, 'action');
                            if (item.close) {
                                setTimeout(() => closeMenu(), 500);
                            }
                        }
                    }
                });
                menu.open();
                logToConsole('Menu opened', 'info');
            }
        }

        // Function to close the menu
        function closeMenu() {
            if (menu) {
                menu.close();
                menu = null;
                logToConsole('Menu closed', 'info');
            }
        }

        // Keyboard controls
        document.addEventListener("keyup", function(event) {
            if (event.key === "F1") {
                event.preventDefault();
                if (menu) {
                    closeMenu();
                } else {
                    showMenu();
                }
            }
            if (event.key === "Escape" && menu) {
                event.preventDefault();
                closeMenu();
            }
        });

        // Initialize
        $(document).ready(function() {
            logToConsole('Radial Menu Test initialized', 'info');
            logToConsole('Press F1 to toggle menu', 'info');

            // Auto-show menu after 1 second
            setTimeout(() => {
                showMenu();
            }, 1000);
        });
    </script>
</body>
</html>

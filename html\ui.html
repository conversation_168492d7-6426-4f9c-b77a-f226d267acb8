<!DOCTYPE html>
<html lang="en">
<head>
    <title>FrameWork Menu</title>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.1/css/all.css" integrity="rx5u3IdaOCszi7Jb18XD9HSn8bNiEgAqWJbdBvIYYyU=" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,500,500i,700,700i" rel="stylesheet">
    <link rel="stylesheet" href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="js/RadialMenu.js"></script>
    <script type="text/javascript" src="js/all.min.js"></script>
    <link type="text/css" rel="stylesheet" href="css/RadialMenu.css">
    <link type="text/css" rel="stylesheet" href="css/all.min.css">
    <script type="text/javascript">
        $(document).ready(function () {
            let menuKeybind = "F1"
            let menu = null;

            // Sample menu data for testing
            const sampleMenuData = [
                {
                    id: 'citizen',
                    title: 'Citizen',
                    icon: 'fas fa-user-circle',
                    functionName: 'citizen_action',
                    functiontype: 'client',
                    functionParameters: {},
                    close: true,
                    items: [
                        {
                            id: 'cuff',
                            title: 'Cuff',
                            icon: 'fas fa-handcuffs',
                            functionName: 'cuff_player',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        },
                        {
                            id: 'escort',
                            title: 'Escort',
                            icon: 'fas fa-people-arrows',
                            functionName: 'escort_player',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        }
                    ]
                },
                {
                    id: 'vehicle',
                    title: 'Vehicle',
                    icon: 'fas fa-car',
                    functionName: 'vehicle_action',
                    functiontype: 'client',
                    functionParameters: {},
                    close: true,
                    items: [
                        {
                            id: 'engine',
                            title: 'Toggle Engine',
                            icon: 'fas fa-engine',
                            functionName: 'toggle_engine',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        },
                        {
                            id: 'doors',
                            title: 'Toggle Doors',
                            icon: 'fas fa-door-open',
                            functionName: 'toggle_doors',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        }
                    ]
                },
                {
                    id: 'animations',
                    title: 'Animations',
                    icon: 'fas fa-theater-masks',
                    functionName: 'animations_menu',
                    functiontype: 'client',
                    functionParameters: {},
                    close: false,
                    items: [
                        {
                            id: 'dance',
                            title: 'Dance',
                            icon: 'fas fa-music',
                            functionName: 'play_dance',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        },
                        {
                            id: 'sit',
                            title: 'Sit',
                            icon: 'fas fa-chair',
                            functionName: 'play_sit',
                            functiontype: 'client',
                            functionParameters: {},
                            close: true
                        }
                    ]
                },
                {
                    id: 'job',
                    title: 'Job Actions',
                    icon: 'fas fa-briefcase',
                    functionName: 'job_actions',
                    functiontype: 'client',
                    functionParameters: {},
                    close: false
                },
                {
                    id: 'inventory',
                    title: 'Inventory',
                    icon: 'fas fa-box-open',
                    functionName: 'open_inventory',
                    functiontype: 'client',
                    functionParameters: {},
                    close: true
                }
            ];

            // Function to show the menu
            function showMenu() {
                if (!menu) {
                    $('#container').append(`<div id="navMenu"></div>`);
                    menu = new RadialMenu({
                        parent: document.getElementById('navMenu'),
                        size: 450,
                        closeOnClick: false,
                        menuItems: sampleMenuData,
                        onClick: function (item) {
                            console.log('Clicked item:', item);
                            if(item.functionName !== 'undefined') {
                                // Instead of posting to NUI, just log the action
                                console.log('Action triggered:', {
                                    action: item.functionName,
                                    type: item.functiontype,
                                    parameters: item.functionParameters
                                });
                                if (item.close) {
                                    closeMenu();
                                }
                            }
                        }
                    });
                    menu.open();
                }
            }

            // Function to close the menu
            function closeMenu() {
                if (menu) {
                    $("#navMenu").remove();
                    menu.destroy();
                    menu = null;
                }
            }

            // Show menu on page load for testing
            showMenu();

            // Add button to toggle menu
            $('body').append('<button id="toggleMenu" style="position: fixed; top: 10px; left: 10px; z-index: 1000; padding: 10px; background: #333; color: white; border: none; border-radius: 5px;">Toggle Menu (F1)</button>');

            $('#toggleMenu').click(function() {
                if (menu) {
                    closeMenu();
                } else {
                    showMenu();
                }
            });

            // Keyboard controls
            window.addEventListener("keyup", function onEvent(event) {
                if (event.key === "F1") {
                    if (menu) {
                        closeMenu();
                    } else {
                        showMenu();
                    }
                }
                if (event.key === "Escape" && menu) {
                    closeMenu();
                }
            });
        });

    </script>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: hidden;
            font-family: "Roboto";
        }

        #container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .menuHolder {
            font-size: 10px;
        }

        #icons {
            display: none;
            filter:url(#drop-shadow);
        }
    </style>
</head>
<body>
    <i data-fa-symbol="more" class="fa fa-ellipsis-h"></i>
    <i data-fa-symbol="globe-map-marked-alt" class="fas fa-map-marked-alt"></i>
    <i data-fa-symbol="globe-map-marker-alt" class="fas fa-map-marker-alt"></i>
    <i data-fa-symbol="globe-europe" class="fas fa-globe-europe"></i>
    <i data-fa-symbol="global-doors" class="fas fa-door-closed"></i>
    <i data-fa-symbol="global-card" class="fas fa-credit-card"></i>
    <i data-fa-symbol="global-bank" class="fas fa-university"></i>
    <i data-fa-symbol="global-store" class="fas fa-shopping-basket"></i>
    <i data-fa-symbol="global-cityhall" class="fas fa-university"></i>
    <i data-fa-symbol="global-appartment" class="fas fa-door-open"></i>
    <i data-fa-symbol="global-depot" class="fas fa-truck"></i>
    <i data-fa-symbol="global-trash" class="fas fa-dumpster"></i>
    <i data-fa-symbol="global-delete" class="fas fa-trash-restore-alt"></i>
    <i data-fa-symbol="global-box" class="fas fa-box"></i>
    <i data-fa-symbol="global-tent" class="fas fa-campground"></i>
    <i data-fa-symbol="global-makelaar" class="fas fa-file-contract"></i>
    <i data-fa-symbol="makelaar-duty" class="fab fa-shirtsinbulk"></i>
    <i data-fa-symbol="makelaar-blips" class="fas fa-house-user"></i>
    <i data-fa-symbol="global-kapper" class="fas fa-cut"></i>
    <i data-fa-symbol="global-garage" class="fas fa-parking"></i>
    <i data-fa-symbol="global-kleren" class="fas fa-hat-cowboy-side"></i>
    <i data-fa-symbol="global-shirt" class="fas fa-tshirt"></i>
    <i data-fa-symbol="global-shoe" class="fas fa-shoe-prints"></i>
    <i data-fa-symbol="global-glasses" class="fas fa-glasses"></i>
    <i data-fa-symbol="global-gloves" class="fas fa-mitten"></i>
    <i data-fa-symbol="global-escort" class="fas fa-people-pulling"></i>
    <i data-fa-symbol="global-steal" class="fas fa-people-robbery"></i>
    <i data-fa-symbol="global-tattoo" class="fas fa-ghost"></i>
    <i data-fa-symbol="global-gas" class="fas fa-gas-pump"></i></i>
    <i data-fa-symbol="global-blips" class="fa fa-route"></i>
    <!-- <i data-fa-symbol="global-tow" class="fa-solid fa-truck-tow"></i> -->
    <i data-fa-symbol="global-clothes" class="fas fa-hat-cowboy-side"></i>
    <i data-fa-symbol="global-radio" class="fas fa-broadcast-tower"></i>
    <i data-fa-symbol="global-bills" class="fas fa-file-invoice-dollar"></i>
    <i data-fa-symbol="mask" class="fas fa-mask"></i>

    <i data-fa-symbol="global-arrow-left" class="fas fa-angle-left"></i>
    <i data-fa-symbol="global-arrow-right" class="fas fa-angle-right"></i>
    <i data-fa-symbol="global-arrow-up" class="fas fa-angle-up"></i>
    <i data-fa-symbol="global-arrow-down" class="fas fa-angle-down"></i>

    <i data-fa-symbol="global-dealer" class="fas fa-user-circle"></i>

    <i data-fa-symbol="global-boat" class="fas fa-boat"></i>
    <!--Politie-->
    <i data-fa-symbol="police-power-off" class="fas fa-power-off"></i>
    <i data-fa-symbol="police-sign-post" class="fas fa-map-signs"></i>
    <i data-fa-symbol="police-person" class="fas fa-male"></i>
    <i data-fa-symbol="police-panic" class="fas fa-exclamation-circle"></i>
    <i data-fa-symbol="police-pin" class="fas fa-map-pin"></i>
    <i data-fa-symbol="police-down" class="far fa-sad-cry"></i>
    <i data-fa-symbol="police-cameras" class="far fa-camera"></i>
    <i data-fa-symbol="police-action" class="fas fa-shield-alt"></i>
    <i data-fa-symbol="police-action-panic" class="fas fa-bullhorn"></i>
    <i data-fa-symbol="police-action-vehicle" class="fas fa-car-alt"></i>
    <i data-fa-symbol="police-action-report" class="fa fa-bullhorn"></i>
    <i data-fa-symbol="police-action-tablet" class="fas fa-tablet-alt"></i>
    <i data-fa-symbol="police-action-search" class="fas fa-search"></i>
    <i data-fa-symbol="police-action-vehicle-spawn" class="fas fa-car-side"></i>
    <i data-fa-symbol="police-action-vehicle-spawn-bus" class="fas fa-truck-pickup"></i>
    <i data-fa-symbol="police-action-vehicle-spawn-heli" class="fas fa-helicopter"></i>
    <i data-fa-symbol="police-action-vehicle-spawn-motor" class="fas fa-motorcycle"></i>
    <i data-fa-symbol="police-action-enkelband" class="fas fa-voicemail"></i>
    <i data-fa-symbol="police-radio-channel" class="fas fa-voicemail"></i>
    <i data-fa-symbol="player-radio-channel" class="fas fa-broadcast-tower"></i>
    <i data-fa-symbol="police-jail" class="fas fa-user-lock"></i>
    <!--Ambulance-->
    <i data-fa-symbol="ambulance-action" class="fas fa-briefcase-medical"></i>
    <i data-fa-symbol="ambulance-action-heal" class="fas fa-band-aid"></i>
    <i data-fa-symbol="ambulance-action-blood" class="fas fa-tint"></i>
    <!--Burger-->
    <i data-fa-symbol="citizen-action" class="fas fa-user-circle"></i>
    <i data-fa-symbol="citizen-action-cuff" class="fas fa-user-lock"></i>
    <i data-fa-symbol="citizen-action-escort" class="fas fa-user-friends"></i>
    <i data-fa-symbol="citizen-action-vehicle" class="fas fa-car-alt"></i>
    <i data-fa-symbol="citizen-action-vehicle-key" class="fas fa-key"></i>
    <i data-fa-symbol="citizen-action-steal" class="fas fa-user-cog"></i>
    <i data-fa-symbol="citizen-put-in-veh" class="fas fa-sign-in-alt"></i>
    <i data-fa-symbol="citizen-put-out-veh" class="fas fa-sign-out-alt"></i>
    <i data-fa-symbol="citizen-contact" class="fas fa-mobile"></i>
    <i data-fa-symbol="citizen-action-cornerselling" class="fas fa-campground"></i>
    <!--Taxi-->
    <i data-fa-symbol="taxi-action" class="fas fa-taxi"></i>
    <i data-fa-symbol="taxi-meter" class="fas fa-tachometer-alt-fast"></i>
    <i data-fa-symbol="taxi-start" class="fa fa-hourglass-start"></i>
    <i data-fa-symbol="taxi-npc" class="fas fa-taxi"></i>
    <!-- Garage -->
    <i data-fa-symbol="citizen-action-garage" class="fas fa-warehouse"></i>
    <!-- Judge -->
    <i data-fa-symbol="judge-actions" class="fas fa-balance-scale"></i>
    <i data-fa-symbol="car-auction" class="fas fa-file-invoice-dollar"></i>

    <!-- Anims & Expression -->
    <i data-fa-symbol="walking" class="fas fa-walking"></i>
    <i data-fa-symbol="animation-default" class="far fa-meh"></i>
    <i data-fa-symbol="animation-brave" class="fab fa-wolf-pack-battalion"></i>
    <i data-fa-symbol="animation-sassy" class="fas fa-kiss"></i>
    <i data-fa-symbol="animation-more" class="fas fa-ellipsis-h"></i>
    <i data-fa-symbol="animation-chichi" class="fas fa-yin-yang"></i>
    <i data-fa-symbol="animation-maneater" class="fas fa-grin-tongue-wink"></i>
    <i data-fa-symbol="animation-shady" class="fas fa-user-ninja"></i>
    <i data-fa-symbol="animation-swagger" class="fas fa-blind"></i>
    <i data-fa-symbol="animation-money" class="fas fa-money-bill-alt"></i>
    <i data-fa-symbol="animation-givecash" class="fas fa-solid fa-money-bill"></i>
    <i data-fa-symbol="animation-hobo" class="fas fa-dumpster"></i>
    <i data-fa-symbol="animation-nonchalant" class="fas fa-meh"></i>
    <i data-fa-symbol="animation-alien" class="fab fa-reddit-alien"></i>
    <i data-fa-symbol="animation-posh" class="fas fa-crown"></i>
    <i data-fa-symbol="animation-sad" class="fas fa-frown"></i>
    <i data-fa-symbol="animation-tough" class="fas fa-dumbbell"></i>
    <i data-fa-symbol="animation-injured" class="fas fa-crutch"></i>
    <i data-fa-symbol="animation-tipsy" class="fas fa-beer"></i>
    <i data-fa-symbol="animation-business" class="fas fa-briefcase"></i>
    <i data-fa-symbol="animation-hurry" class="fas fa-running"></i>
    <i data-fa-symbol="expressions" class="fas fa-theater-masks"></i>
    <i data-fa-symbol="expressions-angry" class="fas fa-angry"></i>
    <i data-fa-symbol="expressions-drunk" class="fas fa-beer"></i>
    <i data-fa-symbol="expressions-dumb" class="fas fa-grin-tongue-squint"></i>
    <i data-fa-symbol="expressions-electrocuted" class="fas fa-bolt"></i>
    <i data-fa-symbol="expressions-grumpy" class="fas fa-tired"></i>
    <i data-fa-symbol="expressions-happy" class="fas fa-smile-beam"></i>
    <i data-fa-symbol="expressions-injured" class="fas fa-user-injured"></i>
    <i data-fa-symbol="expressions-joyful" class="fas fa-laugh"></i>
    <i data-fa-symbol="expressions-mouthbreather" class="fas fa-teeth-open"></i>
    <i data-fa-symbol="expressions-normal" class="fas fa-meh-blank"></i>
    <i data-fa-symbol="expressions-oneeye" class="fas fa-laugh-wink"></i>
    <i data-fa-symbol="expressions-shocked" class="fas fa-bolt"></i>
    <i data-fa-symbol="expressions-sleeping" class="fas fa-bed"></i>
    <i data-fa-symbol="expressions-smug" class="fas fa-meh-rolling-eyes"></i>
    <i data-fa-symbol="expressions-speculative" class="fas fa-lightbulb"></i>
    <i data-fa-symbol="expressions-stressed" class="fas fa-flushed"></i>
    <i data-fa-symbol="expressions-sulking" class="fas fa-sad-cry"></i>
    <i data-fa-symbol="expressions-weird" class="fas fa-grimace"></i>
    <i data-fa-symbol="expressions-weird2" class="far fa-grimace"></i>
    <i data-fa-symbol="customs" class="far fa-warehouse"></i>
    <!-- HTML code -->
    <div id="container">
        <div id="navMenu"></div>
    </div>
</body>
</html>
local DutyVehicles = {}
HasHandCuffs = false

Config = Config or {}

Config.Keys = {["F1"] = 288}
Config.Locale = "en"
Config.Menu = {}
Config.isPlayerDead = function()
    return exports['qb-ambulancejob']:isPlayerDead()
end

Config.Menu[#Config.Menu + 1] = {
    id = "citizen",
    displayName = "Citizen",
    icon = "#citizen-action",
    enableMenu = function()
        if not Config.isPlayerDead() then
            return true
        end
    end,
    subMenus = {"citizen:escort", 'citizen:steal', 'citizen:vehicle:getout', 'citizen:vehicle:getin', 'citizen:Kidnap'}
}

Config.Menu[#Config.Menu + 1] = {
    id = "emotes",
    displayName = "Emotes Menu",
    icon = "#walking",
    close = true,
    functiontype = "client",
    functionName = "animations:client:EmoteMenu",
    enableMenu = function()
        if not Config.isPlayerDead() then
            return true
        end
    end,
}

Config.Menu[#Config.Menu + 1] = {
    id = "animations",
    displayName = "Walkstyles",
    icon = "#walking",
    enableMenu = function()
        if not Config.isPlayerDead() then
            return true
        end
    end,
    subMenus = { "animations:brave", "animations:hurry", "animations:business", "animations:tipsy", "animations:injured","animations:tough", "animations:default", "animations:hobo", "animations:money", "animations:swagger", "animations:shady", "animations:maneater", "animations:chichi", "animations:sassy", "animations:sad", "animations:posh", "animations:alien" }
}
Config.Menu[#Config.Menu + 1] = {
    id = "expressions",
    displayName = "Expressions",
    icon = "#expressions",
    enableMenu = function()
        if not Config.isPlayerDead() then
            return true
        end
    end,
    subMenus = { "expressions:normal", "expressions:drunk", "expressions:angry", "expressions:dumb", "expressions:electrocuted", "expressions:grumpy", "expressions:happy", "expressions:injured", "expressions:joyful", "expressions:mouthbreather", "expressions:oneeye", "expressions:shocked", "expressions:sleeping", "expressions:smug", "expressions:speculative", "expressions:stressed", "expressions:sulking", "expressions:weird", "expressions:weird2"}
}

Config.Menu[#Config.Menu + 1] = {
    id = "mapmanager",
    displayName = "Map Manager",
    icon = "#globe-map-marked-alt",
    close = true,
    functiontype = "client",
    functionName = "qb-slt:client:BlipsManager",
    enableMenu = function()
        if not Config.isPlayerDead() then
            return true
        end
    end,
}

Config.Menu[#Config.Menu + 1] = {
    id = "police",
    displayName = "Police",
    icon = "#police-action",
    enableMenu = function()
        if not Config.isPlayerDead() and (QBCore.Functions.GetPlayerData().job.name == 'police' or QBCore.Functions.GetPlayerData().job.name == 'sheriff') and QBCore.Functions.GetPlayerData().job.onduty then
            return true
        end
    end,
    subMenus = {"police:search", "police:jail", "police:bill", "police:removemask", "police:OfficerBackup", "police:Panic1", 'police:obj'}
}

Config.Menu[#Config.Menu + 1] = {
    id = "police",
    displayName = "Radio",
    icon = "#global-radio",
    enableMenu = function()
        if not Config.isPlayerDead() and (QBCore.Functions.GetPlayerData().job.name == 'police' or QBCore.Functions.GetPlayerData().job.name == 'sheriff' or QBCore.Functions.GetPlayerData().job.name == 'ambulance') and QBCore.Functions.GetPlayerData().job.onduty then
            return true
        end
    end,
    subMenus = {"police:radio:leave", "police:radio:one", "police:radio:two", "police:radio:three", "police:radio:four", "police:radio:five", "police:radio:6", "police:radio:7", "police:radio:8", "police:radio:9", "police:radio:10"}
}
Config.Menu[#Config.Menu + 1] = {
    id = "ambulance",
    displayName = "EMS",
    icon = "#ambulance-action",
    enableMenu = function()
        if not Config.isPlayerDead() and (QBCore.Functions.GetPlayerData().job.name == 'ambulance' and QBCore.Functions.GetPlayerData().job.onduty) then
            return true
        end
    end,
    subMenus = {"ambulance:heal", "ambulance:revive", "ambulance:depot"}
}
Config.Menu[#Config.Menu + 1] = {
    id = "police",
    displayName = "Officer Down",
    icon = "#ambulance-action",
    close = true,
    functiontype = "client",
    functionName = "qb-radialmenu:client:OfficerDown",
    enableMenu = function()
        if Config.isPlayerDead() and (QBCore.Functions.GetPlayerData().job.name == 'police' or QBCore.Functions.GetPlayerData().job.name == 'sheriff') and QBCore.Functions.GetPlayerData().job.onduty then
            return true
        end
    end,
}
Config.Menu[#Config.Menu + 1] = {
    id = "police",
    displayName = "Panic",
    icon = "#ambulance-action",
    close = true,
    functiontype = "client",
    functionName = "qb-radialmenu:client:OfficerDown2",
    enableMenu = function()
        if Config.isPlayerDead() and (QBCore.Functions.GetPlayerData().job.name == 'police' or QBCore.Functions.GetPlayerData().job.name == 'sheriff') and QBCore.Functions.GetPlayerData().job.onduty then
            return true
        end
    end,
}

Config.Menu[#Config.Menu + 1] = {
    id = "vehicleKey",
    displayName = "Vehicle Key",
    icon = "#citizen-action-vehicle-key",
    close = true,
    functiontype = "client",
    functionName = "vehiclekeys:client:GiveKeys",
    enableMenu = function()
        if not Config.isPlayerDead() then
            local Vehicle, Distance = QBCore.Functions.GetClosestVehicle()
            if Vehicle ~= 0 and Distance < 2.3 then
                return true
            end
        end
    end,
}

Config.SubMenus = {



    ["ambulance:heal"] = {
        title = "Heal",
        icon = "#ambulance-action-heal",
        close = true,
        functiontype = "client",
        functionName = "hospital:client:TreatWounds",
    },
    ["ambulance:revive"] = {
        title = "Revive",
        icon = "#ambulance-action-blood",
        close = true,
        functiontype = "client",
        functionName = "hospital:client:RevivePlayer",
    },
    ["ambulance:depot"] = {
        title = "Depot Vehicle",
        icon = "#police-action-vehicle",
        close = true,
        functiontype = "client",
        functionName = "qb-hospital:client:impoundambulancemenu",
    },
    --blips
    ["blips:tattooshop"] = {
        title = "Tattooshop",
        icon = "#global-tattoo",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:tattooshop",
    },

    ["blips:barbershop"] = {
        title = "Barber",
        icon = "#global-kapper",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:barbershop",
    },
    ["blips:garage"] = {
        title = "Garage",
        icon = "#global-garage",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:garage",
    },
    ["blips:gas"] = {
        title = "Petrol station",
        icon = "#global-gas",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:benzine",
    },
    ["blips:clothing"] = {
        title = "clothes shop",
        icon = "#global-kleren",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:clothing",
    },
    ["blips:bennys"] = {
        title = "bennys",
        icon = "#citizen-action-vehicle",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:bennys",
    },
    ['police:radio:leave'] = {
        title = "Leave",
        icon = "#police-power-off",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:leave:radio"
    },
    ['police:radio:one'] = {
        title = "#1",
        icon = "#global-radio",
        close = true,
        functionParameters = 1,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:two'] = {
        title = "#2",
        icon = "#global-radio",
        close = true,
        functionParameters = 2,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:three'] = {
        title = "#3",
        icon = "#global-radio",
        close = true,
        functionParameters = 3,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:four'] = {
        title = "#4",
        icon = "#global-radio",
        close = true,
        functionParameters = 4,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:five'] = {
        title = "#5",
        icon = "#global-radio",
        close = true,
        functionParameters = 5,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:6'] = {
        title = "#6",
        icon = "#global-radio",
        close = true,
        functionParameters = 6,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:7'] = {
        title = "#7",
        icon = "#global-radio",
        close = true,
        functionParameters = 7,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:8'] = {
        title = "#8",
        icon = "#global-radio",
        close = true,
        functionParameters = 8,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:9'] = {
        title = "#9",
        icon = "#global-radio",
        close = true,
        functionParameters = 9,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    ['police:radio:10'] = {
        title = "#10",
        icon = "#global-radio",
        close = true,
        functionParameters = 10,
        functiontype = "client",
        functionName = "qb-radialmenu:client:enter:radio"
    },
    -- important actions
    ['police:search'] = {
        title = "Search",
        icon = "#police-action-search",
        close = true,
        functiontype = "client",
        functionName = "police:client:SearchPlayer"
    },
    ['police:jail'] = {
        title = "Jail",
        icon = "#police-jail",
        close = true,
        functiontype = "client",
        functionName = "police:client:JailPlayer"
    }, 
    ['police:removemask'] = {
        title = "Remove Mask",
        icon = "#mask",
        close = true,
        functiontype = "client",
        functionName = "qb-police:removeMask"
    }, 
    ['police:bill'] = {
        title = "Bill",
        icon = "#global-bills",
        close = true,
        functiontype = "client",
        functionName = "FD_bank_bill:client:billmenu"
    }, 
    -- important actions
    ['police:Panic1'] = {
        title = "Panic",
        icon = "#police-panic",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:OfficerDown2"
    },
    ['police:OfficerBackup'] = {
        title = "Request Pick Up",
        icon = "#globe-map-marker-alt",
        close = true,
        functiontype = "client",
        functionName = "qb-radialmenu:client:OfficerBackup"
    },
    ['police:obj'] = {
    title = "Objects",
    icon = "#police-sign-post",
    close = true,
    functiontype = "client",
    functionName = "police:client:spawnObjects"
    },
    ['police:resetdoor'] = {
     title = "Reset Door",
     icon = "#global-appartment",
     close = true,
     functiontype = "client",
     functionName = "qb-housing:client:reset:house:door"
    },

    -- POLICE VEHICLES START -- 

    ['police:vehicle:touran'] = {
        title = "Volkswagen Touran",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'ptouran',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:touran11'] = {
        title = "Volkswagen Touran",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'ptouran11',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        }, 
    ['police:vehicle:klasse'] = {
        title = "Mercedes B-Klasse",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pbklasse',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:amarok'] = {
        title = "Volkswagen Amarok",
        icon = "#police-action-vehicle-spawn-bus",
        close = true,
        functionParameters = 'pamarok',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pvito'] = {
        title = "Mercedes Vito",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pvito',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:dsivito'] = {
        title = "Vito Unmarked",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'dsivito',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:fiets'] = {
        title = "Fiets",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pfiets',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pbal'] = {
        title = "Unmarked Baller",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pbal',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:audi'] = {
        title = "Audi A6",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'paudi',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:velar'] = {
        title = "Oracle Unmarked",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'poracle',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pyamahamotor'] = {
        title = "Yamaha Motor",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pyamahamotor',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:dsimerc'] = {
        title = "Mercedes (DSI)",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'dsimerc',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:prs6'] = {
        title = "Audi RS6",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'prs6',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:gevang'] = {
        title = "Transport",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pasprinter',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pschafter'] = {
        title = "Unmarked Shafter",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pschafter',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pmas'] = {
        title = "Unmarked Maserati",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pmas',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:dsiq5'] = {
        title = "Unmarked DSIQ 5",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'dsiq5',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:pfosprinter'] = {
        title = "FO Sprinter",
        icon = "#police-action-vehicle-spawn",
        close = true,
        functionParameters = 'pfosprinter',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:heli'] = {
        title = "Zulu",
        icon = "#police-action-vehicle-spawn-heli",
        close = true,
        functionParameters = 'pzulu',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
    ['police:vehicle:motor'] = {
        title = "BMW Motor",
        icon = "#police-action-vehicle-spawn-motor",
        close = true,
        functionParameters = 'pbmwmotor2',
        functiontype = "client",
        functionName = "qb-police:client:spawn:vehicle"
        },
        
    -- POLICE VEHICLES END -- 

    ['police:object:cone'] = {
     title = "Pion",
     icon = "#global-box",
     close = true,
     functionParameters = 'cone',
     functiontype = "client",
     functionName = "qb-police:client:spawn:object"
    },
    ['police:object:barrier'] = {
     title = "Barrier",
     icon = "#global-box",
     close = true,
     functionParameters = 'barrier',
     functiontype = "client",
     functionName = "qb-police:client:spawn:object"
    },
    ['police:object:schot'] = {
     title = "Fence",
     icon = "#global-box",
     close = true,
     functionParameters = 'schot',
     functiontype = "client",
     functionName = "qb-police:client:spawn:object"
    },
    ['police:object:tent'] = {
     title = "Tent",
     icon = "#global-tent",
     close = true,
     functionParameters = 'tent',
     functiontype = "client",
     functionName = "qb-police:client:spawn:object"
    },
    ['police:object:light'] = {
     title = "Lamps",
     icon = "#global-box",
     close = true,
     functionParameters = 'light',
     functiontype = "client",
     functionName = "qb-police:client:spawn:object"
    },
    ['police:object:delete'] = {
     title = "Remove object",
     icon = "#global-delete",
     close = false,
     functiontype = "client",
     functionName = "qb-police:client:delete:object"
    },
    -- ['ambulance:heal'] = {
    --   title = "Heal",
    --   icon = "#ambulance-action-heal",
    --   close = true,
    --   functiontype = "client",
    --   functionName = "qb-hospital:client:heal:closest"
    -- },
    -- ['ambulance:revive'] = {
    --   title = "Revive",
    --   icon = "#ambulance-action-heal",
    --   close = true,
    --   functiontype = "client",
    --   functionName = "qb-hospital:client:revive:closest"
    -- },
    ['ambulance:blood'] = {
      title = "Take Bloodsample",
      icon = "#ambulance-action-blood",
      close = true,
      functiontype = "client",
      functionName = "qb-hospital:client:take:blood:closest"
    },
    ['ambulance:garage:heli'] = {
      title = "Ambulance Heli",
      icon = "#police-action-vehicle-spawn",
      close = true,
      functionParameters = 'alifeliner',
      functiontype = "client",
      functionName = "qb-hospital:client:spawn:vehicle"
    },
    ['ambulance:garage:touran'] = {
     title = "Mercedes Klasse B",
     icon = "#police-action-vehicle-spawn",
     close = true,
     functionParameters = 'aeklasse',
     functiontype = "client",
     functionName = "qb-hospital:client:spawn:vehicle"
    },
    ['ambulance:garage:sprinter'] = {
     title = "Ambulance Sprinter",
     icon = "#police-action-vehicle-spawn",
     close = true,
     functionParameters = 'asprinter',
     functiontype = "client",
     functionName = "qb-hospital:client:spawn:vehicle"
    },
    ['citizen:givecash'] = {
     title = "Give Cash",
     icon = "#animation-givecash",
     close = true,
     functiontype = "client",
     functionName = "qb-giveCash:client:menu"
    },
    ['citizen:escort'] = {
     title = "Escort",
     icon = "#citizen-action-escort",
     close = true,
     functiontype = "client",
     functionName = "police:client:EscortPlayer"
    },
    ['citizen:steal'] = {
     title = "Steal",
     icon = "#citizen-action-steal",
     close = true,
     functiontype = "client",
     functionName = "police:client:RobPlayer"
    },
    ['citizen:vehicle:getout'] = {
     title = "Put out Vehicle",
     icon = "#citizen-put-out-veh",
     close = true,
     functiontype = "client",
     functionName = "police:client:SetPlayerOutVehicle"
    },
    ['citizen:vehicle:getin'] = {
     title = "Put in Vehicle",
     icon = "#citizen-put-in-veh",
     close = true,
     functiontype = "client",
     functionName = "police:client:PutPlayerInVehicle"
    },
    -- ['citizen:bills'] = {
    --  title = "Bills",
    --  icon = "#global-bills",
    --  close = true,
    --  functiontype = "client",
    --  functionName = "police:client:billplayer"
    -- },
    ['citizen:Kidnap'] = {
     title = "Carry",
     icon = "#citizen-action-escort",
     close = true,
     functiontype = "client",
     functionName = "police:client:KidnapPlayer"
    },
    ['vehicle:flip'] = {
     title = "Flip Vehicle",
     icon = "#citizen-action-vehicle",
     close = true,
     functiontype = "client",
     functionName = "qb-radialmenu:client:flip:vehicle"
    },
    -- // Anims and Expression \\ --
    ['animations:brave'] = {
        title = "Brave",
        icon = "#animation-brave",
        close = true,
        functionName = "AnimSet:Brave",
        functiontype = "client",
    },
    ['animations:hurry'] = {
        title = "Hurry",
        icon = "#animation-hurry",
        close = true,
        functionName = "AnimSet:Hurry",
        functiontype = "client",
    },
    ['animations:business'] = {
        title = "Business",
        icon = "#animation-business",
        close = true,
        functionName = "AnimSet:Business",
        functiontype = "client",
    },
    ['animations:tipsy'] = {
        title = "Tipsy",
        icon = "#animation-tipsy",
        close = true,
        functionName = "AnimSet:Tipsy",
        functiontype = "client",
    },
    ['animations:injured'] = {
        title = "Injured",
        icon = "#animation-injured",
        close = true,
        functionName = "AnimSet:Injured",
        functiontype = "client",
    },
    ['animations:tough'] = {
        title = "Tough",
        icon = "#animation-tough",
        close = true,
        functionName = "AnimSet:ToughGuy",
        functiontype = "client",
    },
    ['animations:sassy'] = {
        title = "Sassy",
        icon = "#animation-sassy",
        close = true,
        functionName = "AnimSet:Sassy",
        functiontype = "client",
    },
    ['animations:sad'] = {
        title = "Sad",
        icon = "#animation-sad",
        close = true,
        functionName = "AnimSet:Sad",
        functiontype = "client",
    },
    ['animations:posh'] = {
        title = "Posh",
        icon = "#animation-posh",
        close = true,
        functionName = "AnimSet:Posh",
        functiontype = "client",
    },
    ['animations:alien'] = {
        title = "Alien",
        icon = "#animation-alien",
        close = true,
        functionName = "AnimSet:Alien",
        functiontype = "client",
    },
    ['animations:nonchalant'] =
    {
        title = "Nonchalant",
        icon = "#animation-nonchalant",
        close = true,
        functionName = "AnimSet:NonChalant",
        functiontype = "client",
    },
    ['animations:hobo'] = {
        title = "Hobo",
        icon = "#animation-hobo",
        close = true,
        functionName = "AnimSet:Hobo",
        functiontype = "client",
    },
    ['animations:money'] = {
        title = "Money",
        icon = "#animation-money",
        close = true,
        functionName = "AnimSet:Money",
        functiontype = "client",
    },
    ['animations:swagger'] = {
        title = "Swagger",
        icon = "#animation-swagger",
        close = true,
        functionName = "AnimSet:Swagger",
        functiontype = "client",
    },
    ['animations:shady'] = {
        title = "Shady",
        icon = "#animation-shady",
        close = true,
        functionName = "AnimSet:Shady",
        functiontype = "client",
    },
    ['animations:maneater'] = {
        title = "Man Eater",
        icon = "#animation-maneater",
        close = true,
        functionName = "AnimSet:ManEater",
        functiontype = "client",
    },
    ['animations:chichi'] = {
        title = "ChiChi",
        icon = "#animation-chichi",
        close = true,
        functionName = "AnimSet:ChiChi",
        functiontype = "client",
    },
    ['animations:default'] = {
        title = "Default",
        icon = "#animation-default",
        close = true,
        functionName = "AnimSet:default",
        functiontype = "client",
    },
    ["expressions:angry"] = {
        title="Angry",
        icon="#expressions-angry",
        close = true,
        functionName = "expressions",
        functionParameters =  { "mood_angry_1" },
        functiontype = "client",
    },
    ["expressions:drunk"] = {
        title="Drunk",
        icon="#expressions-drunk",
        close = true,
        functionName = "expressions",
        functionParameters =  { "mood_drunk_1" },
        functiontype = "client",
    },
    ["expressions:dumb"] = {
        title="Dumb",
        icon="#expressions-dumb",
        close = true,
        functionName = "expressions",
        functionParameters =  { "pose_injured_1" },
        functiontype = "client",
    },
    ["expressions:electrocuted"] = {
        title="Electrocuted",
        icon="#expressions-electrocuted",
        close = true,
        functionName = "expressions",
        functionParameters =  { "electrocuted_1" },
        functiontype = "client",
    },
    ["expressions:grumpy"] = {
        title="Grumpy",
        icon="#expressions-grumpy",
        close = true,
        functionName = "expressions", 
        functionParameters =  { "mood_drivefast_1" },
        functiontype = "client",
    },
    ["expressions:happy"] = {
        title="Happy",
        icon="#expressions-happy",
        close = true,
        functionName = "expressions",
        functionParameters =  { "mood_happy_1" },
        functiontype = "client",
    },
    ["expressions:injured"] = {
        title="Injured",
        icon="#expressions-injured",
        close = true,
        functionName = "expressions",
        functionParameters =  { "mood_injured_1" },
        functiontype = "client",
    },
    ["expressions:joyful"] = {
        title="Joyful",
        icon="#expressions-joyful",
        close = true,
        functionName = "expressions",
        functionParameters =  { "mood_dancing_low_1" },
        functiontype = "client",
    },
    ["expressions:mouthbreather"] = {
        title="Mouthbreather",
        icon="#expressions-mouthbreather",
        close = true,
        functionName = "expressions",
        functionParameters = { "smoking_hold_1" },
        functiontype = "client",
    },
    ["expressions:normal"]  = {
        title="Normal",
        icon="#expressions-normal",
        close = true,
        functionName = "expressions:clear",
        functiontype = "client",
    },
    ["expressions:oneeye"]  = {
        title="One Eye",
        icon="#expressions-oneeye",
        close = true,
        functionName = "expressions",
        functionParameters = { "pose_aiming_1" },
        functiontype = "client",
    },
    ["expressions:shocked"]  = {
        title="Shocked",
        icon="#expressions-shocked",
        close = true,
        functionName = "expressions",
        functionParameters = { "shocked_1" },
        functiontype = "client",
    },
    ["expressions:sleeping"]  = {
        title="Sleeping",
        icon="#expressions-sleeping",
        close = true,
        functionName = "expressions",
        functionParameters = { "dead_1" },
        functiontype = "client",
    },
    ["expressions:smug"]  = {
        title="Smug",
        icon="#expressions-smug",
        close = true,
        functionName = "expressions",
        functionParameters = { "mood_smug_1" },
        functiontype = "client",
    },
    ["expressions:speculative"]  = {
        title="Speculative",
        icon="#expressions-speculative",
        close = true,
        functionName = "expressions",
        functionParameters = { "mood_aiming_1" },
        functiontype = "client",
    },
    ["expressions:stressed"]  = {
        title="Stressed",
        icon="#expressions-stressed",
        close = true,
        functionName = "expressions",
        functionParameters = { "mood_stressed_1" },
        functiontype = "client",
    },
    ["expressions:sulking"]  = {
        title="Sulking",
        icon="#expressions-sulking",
        close = true,
        functionName = "expressions",
        functionParameters = { "mood_sulk_1" },
        functiontype = "client",
    },
    ["expressions:weird"]  = {
        title="Weird",
        icon="#expressions-weird",
        close = true,
        functionName = "expressions",
        functionParameters = { "effort_2" },
        functiontype = "client",
    },
    ["expressions:weird2"]  = {
        title="Weird 2",
        icon="#expressions-weird2",
        close = true,
        functionName = "expressions",
        functionParameters = { "effort_3" },
        functiontype = "client",
    },
	['vehicle:extra3'] = {
        title = "Extra3",
        icon = "#vehicle-plus",
        close = false,
        functionParameters = 3,
        functiontype = "client",
        functionName = "qb-radialmenu:client:setExtra"
    },
}

















----clothes
Config.ExtrasEnabled = false

Config.Commands = {
    ["top"] = {
        Func = function() ToggleClothing("Top") end,
        Sprite = "top",
        Desc = "Take your shirt off/on",
        Button = 1,
        Name = "Torso"
    },
    ["gloves"] = {
        Func = function() ToggleClothing("gloves") end,
        Sprite = "gloves",
        Desc = "Take your gloves off/on",
        Button = 2,
        Name = "Gloves"
    },
    ["visor"] = {
        Func = function() ToggleProps("visor") end,
        Sprite = "visor",
        Desc = "Toggle hat variation",
        Button = 3,
        Name = "Visor"
    },
    ["bag"] = {
        Func = function() ToggleClothing("Bag") end,
        Sprite = "bag",
        Desc = "Opens or closes your bag",
        Button = 8,
        Name = "Bag"
    },
    ["shoes"] = {
        Func = function() ToggleClothing("Shoes") end,
        Sprite = "shoes",
        Desc = "Take your shoes off/on",
        Button = 5,
        Name = "Shoes"
    },
    ["vest"] = {
        Func = function() ToggleClothing("Vest") end,
        Sprite = "vest",
        Desc = "Take your vest off/on",
        Button = 14,
        Name = "Vest"
    },
    ["hair"] = {
        Func = function() ToggleClothing("hair") end,
        Sprite = "hair",
        Desc = "Put your hair up/down/in a bun/ponytail.",
        Button = 7,
        Name = "Hair"
    },
    ["hat"] = {
        Func = function() ToggleProps("Hat") end,
        Sprite = "hat",
        Desc = "Take your hat off/on",
        Button = 4,
        Name = "Hat"
    },
    ["glasses"] = {
        Func = function() ToggleProps("Glasses") end,
        Sprite = "glasses",
        Desc = "Take your glasses off/on",
        Button = 9,
        Name = "Glasses"
    },
    ["ear"] = {
        Func = function() ToggleProps("Ear") end,
        Sprite = "ear",
        Desc = "Take your ear accessory off/on",
        Button = 10,
        Name = "Ear"
    },
    ["neck"] = {
        Func = function() ToggleClothing("Neck") end,
        Sprite = "neck",
        Desc = "Take your neck accessory off/on",
        Button = 11,
        Name = "Neck"
    },
    ["watch"] = {
        Func = function() ToggleProps("Watch") end,
        Sprite = "watch",
        Desc = "Take your watch off/on",
        Button = 12,
        Name = "Watch",
        Rotation = 5.0
    },
    ["bracelet"] = {
        Func = function() ToggleProps("Bracelet") end,
        Sprite = "bracelet",
        Desc = "Take your bracelet off/on",
        Button = 13,
        Name = "Bracelet"
    },
    ["mask"] = {
        Func = function() ToggleClothing("Mask") end,
        Sprite = "mask",
        Desc = "Take your mask off/on",
        Button = 6,
        Name = "Mask"
    }
}

Config.ExtraCommands = {
    ["pants"] = {
        Func = function() ToggleClothing("Pants", true) end,
        Sprite = "pants",
        Desc = "Take your pants off/on",
        Name = "Pants",
        OffsetX = -0.04,
        OffsetY = 0.0
    },
    ["shirt"] = {
        Func = function() ToggleClothing("Shirt", true) end,
        Sprite = "shirt",
        Desc = "Take your shirt off/on",
        Name = "shirt",
        OffsetX = 0.04,
        OffsetY = 0.0
    },
    ["reset"] = {
        Func = function()
            if not ResetClothing(true) then
                Notify('Nothing To Reset', 'error')
            end
        end,
        Sprite = "reset",
        Desc = "Revert everything back to normal",
        Name = "reset",
        OffsetX = 0.12,
        OffsetY = 0.2,
        Rotate = true
    },
    ["bagoff"] = {
        Func = function() ToggleClothing("Bagoff", true) end,
        Sprite = "bagoff",
        SpriteFunc = function()
            local Bag = GetPedDrawableVariation(PlayerPedId(), 5)
            local BagOff = LastEquipped["Bagoff"]
            if LastEquipped["Bagoff"] then
                if bags[BagOff.Drawable] then
                    return "bagoff"
                else
                    return "paraoff"
                end
            end
            if Bag ~= 0 then
                if bags[Bag] then
                    return "bagoff"
                else
                    return "paraoff"
                end
            else
                return false
            end
        end,
        Desc = "Take your bag off/on",
        Name = "bagoff",
        OffsetX = -0.12,
        OffsetY = 0.2
    }
}
